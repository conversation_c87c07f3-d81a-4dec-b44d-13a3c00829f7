@echo off
chcp 65001 >nul
title مدير n8n

echo.
echo ========================================
echo           مدير n8n
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت. يرجى تثبيت Python أولاً.
    echo يمكنك تحميله من: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM التحقق من وجود pip
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ pip غير متوفر.
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

REM تثبيت المكتبات المطلوبة
echo 📦 تثبيت المكتبات المطلوبة...
pip install flask requests >nul 2>&1

if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المكتبات المطلوبة
    echo جاري المحاولة مرة أخرى...
    pip install flask requests
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المكتبات. تحقق من اتصال الإنترنت.
        pause
        exit /b 1
    )
)

echo ✅ تم تثبيت المكتبات بنجاح
echo.

REM التحقق من وجود Docker
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  تحذير: Docker غير مثبت أو غير متاح
    echo يرجى التأكد من تثبيت Docker Desktop وتشغيله
    echo.
)

REM التحقق من وجود docker-compose.yml
if not exist "docker-compose.yml" (
    echo ❌ ملف docker-compose.yml غير موجود
    echo تأكد من وجود الملف في نفس المجلد
    pause
    exit /b 1
)

echo ✅ جميع المتطلبات متوفرة
echo.
echo 🚀 بدء تشغيل مدير n8n...
echo 📱 سيتم فتح المتصفح تلقائياً على: http://localhost:8080
echo ⚡ للإيقاف اضغط Ctrl+C
echo.

REM تشغيل مدير n8n
python n8n_manager.py

pause
