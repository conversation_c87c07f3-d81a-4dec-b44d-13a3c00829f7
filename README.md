# n8n Docker Setup

هذا المشروع يحتوي على إعداد n8n باستخدام Docker Compose.

## المتطلبات
- Docker
- Docker Compose

## التشغيل

### 1. تشغيل n8n
```bash
docker-compose up -d
```

### 2. الوصول إلى n8n
افتح المتصفح واذهب إلى: http://localhost:5678

### 3. الإعداد الأولي
عند الدخول لأول مرة، ستحتاج إلى إنشاء حساب مستخدم جديد.

### 4. إيقاف n8n
```bash
docker-compose down
```

### 5. إيقاف n8n مع حذف البيانات
```bash
docker-compose down -v
```

## المجلدات
- `workflows/`: يحتوي على ملفات workflows المحفوظة
- `credentials/`: يحتوي على بيانات الاعتماد المحفوظة

## تخصيص الإعدادات
يمكنك تعديل متغيرات البيئة في ملف `docker-compose.yml`:
- `N8N_BASIC_AUTH_USER`: اسم المستخدم
- `N8N_BASIC_AUTH_PASSWORD`: كلمة المرور
- `N8N_PORT`: المنفذ (افتراضي: 5678)

## تحديث n8n
```bash
docker-compose pull
docker-compose up -d
```

## عرض السجلات
```bash
docker-compose logs -f n8n
```
