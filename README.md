# n8n Docker Setup

هذا المشروع يحتوي على إعداد n8n باستخدام Docker Compose.

## المتطلبات
- Docker
- Docker Compose

## التشغيل

### 1. تشغيل n8n
```bash
docker-compose up -d
```

### 2. الوصول إلى n8n
افتح المتصفح واذهب إلى: http://localhost:5678

### 3. الإعداد الأولي
عند الدخول لأول مرة، ستحتاج إلى إنشاء حساب مستخدم جديد.

### 4. إيقاف n8n
```bash
docker-compose down
```

### 5. إيقاف n8n مع حذف البيانات
```bash
docker-compose down -v
```

## المجلدات
- `workflows/`: يحتوي على ملفات workflows المحفوظة
- `credentials/`: يحتوي على بيانات الاعتماد المحفوظة

## تخصيص الإعدادات
يمكنك تعديل متغيرات البيئة في ملف `docker-compose.yml`:
- `N8N_HOST`: عنوان الخادم (افتراضي: 0.0.0.0)
- `N8N_PORT`: المنفذ (افتراضي: 5678)
- `WEBHOOK_URL`: رابط الـ webhooks

## تحديث n8n
```bash
docker-compose pull
docker-compose up -d
```

## عرض السجلات
```bash
docker-compose logs -f n8n
```

## 🎛️ مدير n8n الرسومي

تم إنشاء واجهة رسومية سهلة لإدارة n8n مع أزرار التشغيل والإيقاف:

### التشغيل السريع:

**Windows:**
```bash
start_manager.bat
```

**Linux/macOS:**
```bash
chmod +x start_manager.sh
./start_manager.sh
```

**أو يدوياً:**
```bash
python n8n_manager.py
```

### المميزات:
- ✅ **أزرار تشغيل وإيقاف** سهلة الاستخدام
- 💾 **حفظ تلقائي للبيانات** - لن تفقد أي مشروع
- 📊 **مراقبة الحالة** في الوقت الفعلي
- 📋 **عرض السجلات** مباشرة من الواجهة
- 🔄 **إعادة تشغيل سريعة** بضغطة زر
- 🌐 **فتح n8n مباشرة** من الواجهة

### الوصول للمدير:
افتح المتصفح على: **http://localhost:8080**

### ملاحظات مهمة:
- جميع المشاريع والـ workflows محفوظة في مجلد `workflows/`
- بيانات الاعتماد محفوظة في مجلد `credentials/`
- يمكنك إيقاف وتشغيل n8n دون فقدان أي بيانات
- المدير يعمل بشكل مستقل عن n8n
